import { ScoutedPlayer } from '../hooks/useQueries';
import { ActiveTransfer } from '../hooks/useMyBidsPlayers';
import { Manager } from '../models/manager';
import { Player, TransferListPlayer } from '../models/player';
import { Team } from '../models/team';
import { Fixture } from '../models/fixture';

export interface CachedPlayerData {
  teamPlayers: Player[];
  transferListPlayers: TransferListPlayer[];
  scoutedPlayers: ScoutedPlayer[];
  myBidsPlayers: TransferListPlayer[];
  myActiveTransfers: ActiveTransfer[];
}

export interface DataCache {
  manager: Manager | null;
  team: Team | null;
  players: CachedPlayerData;
  fixtures: Fixture[];
  lastUpdated: {
    manager: number;
    team: number;
    players: number;
    fixtures: number;
  };
}

export const createEmptyCache = (): DataCache => ({
  manager: null,
  team: null,
  players: {
    teamPlayers: [],
    transferListPlayers: [],
    scoutedPlayers: [],
    myBidsPlayers: [],
    myActiveTransfers: [],
  },
  fixtures: [],
  lastUpdated: {
    manager: 0,
    team: 0,
    players: 0,
    fixtures: 0,
  },
});

/**
 * Update a player in the cache across all player arrays
 */
export const updatePlayerInCache = (
  cache: DataCache,
  updatedPlayer: Partial<Player> & { playerId: string }
): DataCache => {
  const updatePlayerInArray = <T extends Player | TransferListPlayer | ScoutedPlayer>(
    players: T[]
  ): T[] =>
    players.map((player) =>
      player.playerId === updatedPlayer.playerId ? { ...player, ...updatedPlayer } : player
    );

  return {
    ...cache,
    team: cache.team
      ? {
          ...cache.team,
          players: updatePlayerInArray(cache.team.players),
        }
      : cache.team,
    players: {
      teamPlayers: updatePlayerInArray(cache.players.teamPlayers),
      transferListPlayers: updatePlayerInArray(cache.players.transferListPlayers),
      scoutedPlayers: updatePlayerInArray(cache.players.scoutedPlayers),
      myBidsPlayers: updatePlayerInArray(cache.players.myBidsPlayers),
      myActiveTransfers: cache.players.myActiveTransfers.map((transfer) =>
        transfer.player.playerId === updatedPlayer.playerId
          ? { ...transfer, player: { ...transfer.player, ...updatedPlayer } }
          : transfer
      ),
    },
    lastUpdated: {
      ...cache.lastUpdated,
      players: Date.now(),
    },
  };
};

/**
 * Update manager data in cache
 */
export const updateManagerInCache = (
  cache: DataCache,
  updatedManager: Partial<Manager>
): DataCache => ({
  ...cache,
  manager: cache.manager ? { ...cache.manager, ...updatedManager } : cache.manager,
  lastUpdated: {
    ...cache.lastUpdated,
    manager: Date.now(),
  },
});

/**
 * Update team data in cache
 */
export const updateTeamInCache = (cache: DataCache, updatedTeam: Partial<Team>): DataCache => ({
  ...cache,
  team: cache.team ? { ...cache.team, ...updatedTeam } : cache.team,
  lastUpdated: {
    ...cache.lastUpdated,
    team: Date.now(),
  },
});

/**
 * Find a player across all cached player arrays
 */
export const findPlayerInCache = (
  cache: DataCache,
  playerId: string
): Player | TransferListPlayer | ScoutedPlayer | null => {
  // Check team players first
  if (cache.team) {
    const teamPlayer = cache.team.players.find((p) => p.playerId === playerId);
    if (teamPlayer) return teamPlayer;
  }

  // Check other player arrays
  const { teamPlayers, transferListPlayers, scoutedPlayers, myBidsPlayers, myActiveTransfers } = cache.players;

  // Check each array in order
  const teamPlayer = teamPlayers.find((p) => p.playerId === playerId);
  if (teamPlayer) return teamPlayer;

  const transferListPlayer = transferListPlayers.find((p) => p.playerId === playerId);
  if (transferListPlayer) return transferListPlayer;

  const scoutedPlayer = scoutedPlayers.find((p) => p.playerId === playerId);
  if (scoutedPlayer) return scoutedPlayer;

  const myBidsPlayer = myBidsPlayers.find((p) => p.playerId === playerId);
  if (myBidsPlayer) return myBidsPlayer;

  // For active transfers, we return null since the player structure is different
  // and we don't want to force a conversion that might be incorrect
  return null;
};

/**
 * Check if cache data is stale (older than specified minutes)
 */
export const isCacheStale = (lastUpdated: number, maxAgeMinutes: number = 5): boolean => {
  const maxAge = maxAgeMinutes * 60 * 1000; // Convert to milliseconds
  return Date.now() - lastUpdated > maxAge;
};
